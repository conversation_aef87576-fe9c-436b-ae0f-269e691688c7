import React, { useState, useContext } from 'react';
import { Tag, Button, Typography, Spin, Empty } from 'antd';
import { useRequest, useDeepCompareEffect } from 'ahooks';
import dayjs from 'dayjs';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import PoiListContext from '../../context';
import './index.scss';

const { Text, Paragraph } = Typography;

type SubTaskListResponse =
    APISpec['/xianfu/api-v2/dove/data/poi/subtask']['response'];

const SubTaskList = ({ poi }) => {
    const { filters } = useContext(PoiListContext);
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 3,
    });
    const [allSubtasks, setAllSubtasks] = useState<SubTaskListResponse['data']>(
        [],
    );
    const [totalCount, setTotalCount] = useState(0);

    // 获取子任务列表
    const { loading, run: fetchSubtasks } = useRequest(
        async (isLoadMore = false) => {
            try {
                const requestData = {
                    poiId: poi.id,
                    poiType: -3,
                    page: isLoadMore ? pagination.page : 1,
                    pageSize: pagination.pageSize,
                    subtaskFilter: filters?.subtaskFilter,
                };

                const res = await apiCaller.post(
                    '/xianfu/api-v2/dove/data/poi/subtask',
                    requestData,
                );
                if (res.code === 0 && res.data) {
                    const responseData = res.data as SubTaskListResponse;
                    if (isLoadMore) {
                        // 加载更多时，追加数据
                        setAllSubtasks(prev => [
                            ...prev,
                            ...(responseData.data || []),
                        ]);
                    } else {
                        // 首次加载或刷新时，替换数据
                        setAllSubtasks(responseData.data || []);
                        setTotalCount(responseData.total || 0);
                    }
                    return responseData;
                } else {
                    throw new Error(res?.msg || '获取子任务列表失败');
                }
            } catch (error) {
                console.error('获取子任务列表失败:', error);
                throw error;
            }
        },
        { manual: true },
    );

    useDeepCompareEffect(() => {
        if (!filters?.subtaskFilter) return;
        // 重置状态并获取第一页数据
        setPagination({ page: 1, pageSize: 3 });
        fetchSubtasks(false);
    }, [filters?.subtaskFilter, poi?.poiId]);

    // 处理展开更多
    const handleLoadMore = () => {
        const nextPage = pagination.page + 1;
        setPagination(prev => ({
            ...prev,
            page: nextPage,
        }));
        fetchSubtasks(true);
    };

    // 获取任务状态标签颜色
    const getStatusTagColor = (status?: string) => {
        switch (status) {
            case 'COMPLETED':
                return 'success';
            case 'IN_PROGRESS':
                return 'processing';
            case 'PENDING':
                return 'warning';
            case 'FAILED':
                return 'error';
            default:
                return 'default';
        }
    };

    // 获取任务状态文本
    const getStatusText = (status?: string) => {
        switch (status) {
            case 'COMPLETED':
                return '已完成';
            case 'IN_PROGRESS':
                return '进行中';
            case 'PENDING':
                return '待执行';
            case 'FAILED':
                return '失败';
            default:
                return status || '未知';
        }
    };

    if (!allSubtasks || allSubtasks.length === 0) {
        return (
            <div className="subtask-list__empty">
                <Empty
                    description="暂无子任务"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            </div>
        );
    }

    const hasMore = totalCount && allSubtasks.length < totalCount;

    return (
        <Spin spinning={loading} tip="加载中...">
            <div className="subtask-list">
                <div className="subtask-list__content">
                    {allSubtasks.map((task, index) => (
                        <div key={task.id || index} className="subtask-item">
                            <div className="subtask-item__header">
                                <div className="subtask-item__title">
                                    <Text
                                        strong
                                        ellipsis={{ tooltip: task.taskName }}
                                    >
                                        {task.taskName || '未知任务'}
                                    </Text>
                                </div>
                                <Tag color={getStatusTagColor(task.status)}>
                                    {getStatusText(task.status)}
                                </Tag>
                            </div>

                            <div className="subtask-item__content">
                                {task.agentName && (
                                    <div className="subtask-item__agent">
                                        <Text
                                            type="secondary"
                                            style={{ fontSize: '12px' }}
                                        >
                                            Agent: {task.agentName}
                                        </Text>
                                    </div>
                                )}

                                {task.contactType && (
                                    <div className="subtask-item__contact">
                                        <Text
                                            type="secondary"
                                            style={{ fontSize: '12px' }}
                                        >
                                            触达方式: {task.contactType}
                                        </Text>
                                    </div>
                                )}

                                {task.createTime && (
                                    <div className="subtask-item__time">
                                        <Text
                                            type="secondary"
                                            style={{ fontSize: '12px' }}
                                        >
                                            创建时间:{' '}
                                            {dayjs(task.createTime).format(
                                                'MM-DD HH:mm',
                                            )}
                                        </Text>
                                    </div>
                                )}

                                {task.summary && (
                                    <div className="subtask-item__summary">
                                        <Paragraph
                                            ellipsis={{
                                                rows: 2,
                                                tooltip: task.summary,
                                            }}
                                            type="secondary"
                                            style={{
                                                fontSize: '12px',
                                                margin: 0,
                                            }}
                                        >
                                            {task.summary}
                                        </Paragraph>
                                    </div>
                                )}
                            </div>
                        </div>
                    ))}
                </div>

                {hasMore && (
                    <div className="subtask-list__footer">
                        <Button
                            type="link"
                            size="small"
                            onClick={handleLoadMore}
                            loading={loading}
                            className="subtask-list__load-more"
                        >
                            展开更多
                        </Button>
                    </div>
                )}
            </div>
        </Spin>
    );
};

export default SubTaskList;
