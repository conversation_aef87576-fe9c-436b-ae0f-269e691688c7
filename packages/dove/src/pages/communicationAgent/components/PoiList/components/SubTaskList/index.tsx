import { useState, useContext, useMemo } from 'react';
import { Tag, Button, Typography, Spin, Empty } from 'antd';
import { useRequest, useDeepCompareEffect } from 'ahooks';
import dayjs from 'dayjs';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import PoiListContext from '../../context';
import './index.scss';

const { Text, Paragraph } = Typography;

type SubTaskListResponse =
    APISpec['/xianfu/api-v2/dove/data/poi/subtask']['response'];

const SubTaskList = ({ poi }) => {
    const { filters } = useContext(PoiListContext);
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 3,
    });
    const [allSubtasks, setAllSubtasks] = useState<any[]>([]);
    const [totalCount, setTotalCount] = useState(0);

    // 获取子任务列表
    const { loading, run: fetchSubtasks } = useRequest(
        async (isLoadMore = false) => {
            try {
                const requestData = {
                    poiId: poi.id,
                    poiType: poi.poiType,
                    page: isLoadMore ? pagination.page : 1,
                    pageSize: pagination.pageSize,
                    subtaskFilter: filters?.subtaskFilter,
                };

                const res = await apiCaller.post(
                    '/xianfu/api-v2/dove/data/poi/subtask',
                    requestData,
                );
                if (res.code === 0 && res.data) {
                    const responseData = res.data as SubTaskListResponse;
                    if (isLoadMore) {
                        // 加载更多时，追加数据
                        setAllSubtasks(prev => [
                            ...prev,
                            ...(responseData.data || []),
                        ]);
                    } else {
                        // 首次加载或刷新时，替换数据
                        setAllSubtasks(responseData.data || []);
                        setTotalCount(responseData.total || 0);
                    }
                    return responseData;
                } else {
                    throw new Error(res?.msg || '获取子任务列表失败');
                }
            } catch (error) {
                console.error('获取子任务列表失败:', error);
                throw error;
            }
        },
        { manual: true },
    );

    useDeepCompareEffect(() => {
        if (!filters?.subtaskFilter) return;
        // 重置状态并获取第一页数据
        setPagination({ page: 1, pageSize: 3 });
        fetchSubtasks(false);
    }, [filters?.subtaskFilter, poi?.poiId]);

    // 处理展开更多
    const handleLoadMore = () => {
        const nextPage = pagination.page + 1;
        setPagination(prev => ({
            ...prev,
            page: nextPage,
        }));
        fetchSubtasks(true);
    };

    // 处理查看沟通记录
    const handleViewCommunicationRecord = (task: any) => {
        // TODO: 实现查看沟通记录功能
        // 这里可以打开模态框或跳转到详情页面
        console.log('查看沟通记录:', task);
        // 可以调用相关接口获取详细的沟通记录
        // 或者触发父组件的回调函数
    };

    // 获取任务状态标签颜色
    const getStatusTagColor = (status?: string) => {
        switch (status) {
            case 'COMPLETED':
                return 'success';
            case 'IN_PROGRESS':
                return 'processing';
            case 'PENDING':
                return 'warning';
            case 'FAILED':
                return 'error';
            default:
                return 'default';
        }
    };

    // 获取任务状态文本
    const getStatusText = (status?: string) => {
        switch (status) {
            case 'COMPLETED':
                return '已完成';
            case 'IN_PROGRESS':
                return '进行中';
            case 'PENDING':
                return '待执行';
            case 'FAILED':
                return '失败';
            default:
                return status || '未知';
        }
    };

    if (!allSubtasks || allSubtasks.length === 0) {
        return (
            <div className="subtask-list__empty">
                <Empty
                    description="暂无子任务"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            </div>
        );
    }

    const hasMore = totalCount && allSubtasks.length < totalCount;

    const handleTaskBaseInfo = task => {
        return [
            {
                label: '创建人',
                value: task.creator,
            },
            {
                label: '创建时间',
                value: task.createTime
                    ? dayjs(task.createTime).format('YYYY-MM-DD HH:mm')
                    : '-',
            },
            {
                label: '执行状态',
                value: getStatusText(task.status),
            },
            {
                label: '关联Agent',
                value: `${task.agentName}（${task.agentId}）` || '-',
            },
            {
                label: '触达方式',
                value: task.contactType,
            },
            {
                label: '调度流程',
                value: task.processName,
            },
            {
                label: '跟进人',
                value: task.followMis,
            },
        ].filter(item => item.value);
    };

    const handleTaskImportantInfo = task => {
        return [
            {
                label: '触达结果',
                value: task.reachStatus,
            },
            {
                label: '失败原因',
                value: task.failureReason,
            },
            {
                label: '触达时间',
                value: task.reachTime
                    ? dayjs(task.reachTime).format('YYYY-MM-DD HH:mm')
                    : '-',
            },
        ].filter(item => item.value);
    };

    return (
        <Spin spinning={loading} tip="加载中...">
            <div className="subtask-list">
                <div className="subtask-list__content">
                    {allSubtasks.map((task, index) => (
                        <div key={task.id || index} className="subtask-item">
                            <div className="subtask-item__header">
                                <div className="subtask-item__title">
                                    <Text
                                        strong
                                        ellipsis={{
                                            tooltip: `${task.taskName}  ID：${task.id}`,
                                        }}
                                    >
                                        {`${task.taskName}  ID：${task.id}`}
                                    </Text>
                                </div>
                                <Tag color={getStatusTagColor(task.status)}>
                                    {getStatusText(task.status)}
                                </Tag>
                            </div>

                            <div className="subtask-item__content">
                                {/* 任务类型信息行 */}
                                <div className="subtask-item__task-info">
                                    {handleTaskBaseInfo(task).map(
                                        (item, index) => (
                                            <Text
                                                key={index}
                                                type="secondary"
                                                style={{ fontSize: '12px' }}
                                            >
                                                {item.label}：{item.value}
                                            </Text>
                                        ),
                                    )}
                                </div>
                                <div className="subtask-item__task-info important">
                                    {handleTaskImportantInfo(task).map(
                                        (item, index) => (
                                            <Text
                                                key={index}
                                                type="secondary"
                                                style={{ fontSize: '12px' }}
                                            >
                                                {item.label}：{item.value}
                                            </Text>
                                        ),
                                    )}
                                </div>
                                {/* AI总结 */}
                                {!task.summary && (
                                    <div className="subtask-item__summary">
                                        <Text
                                            type="secondary"
                                            style={{
                                                fontSize: '12px',
                                                fontWeight: 'bold',
                                            }}
                                        >
                                            AI总结：
                                        </Text>
                                        查看沟通记录查看沟通记录{task.summary}
                                    </div>
                                )}
                                {/* 操作按钮 */}
                                <div className="subtask-item__actions">
                                    <Button
                                        type="link"
                                        size="small"
                                        style={{ padding: 0, fontSize: '12px' }}
                                        onClick={() =>
                                            handleViewCommunicationRecord(task)
                                        }
                                    >
                                        查看沟通记录
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {hasMore && (
                    <div className="subtask-list__footer">
                        <Button
                            type="link"
                            size="small"
                            onClick={handleLoadMore}
                            loading={loading}
                            className="subtask-list__load-more"
                        >
                            展开更多
                        </Button>
                    </div>
                )}
            </div>
        </Spin>
    );
};

export default SubTaskList;
