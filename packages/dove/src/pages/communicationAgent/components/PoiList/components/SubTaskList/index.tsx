import React, { useState, useEffect } from 'react';
import {
    List,
    Tag,
    Button,
    Space,
    Typography,
    Pagination,
    Spin,
    Empty,
    Alert,
    Divider,
} from 'antd';
import { useRequest, useUpdateEffect } from 'ahooks';
import dayjs from 'dayjs';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import type { SubTaskListProps } from '../../types';
import './index.scss';

// 根据API接口定义的子任务数据类型
type SubTaskDTO = {
    id?: string;
    tel?: string;
    rank?: string;
    bizId?: number;
    status?: string;
    taskId?: string;
    agentId?: string;
    bizName?: string;
    creator?: string;
    summary?: string;
    duration?: number;
    taskName?: string;
    agentName?: string;
    followMis?: string;
    followUid?: string;
    processId?: string;
    reachTime?: number;
    createTime?: number;
    contactType?: string;
    processName?: string;
    reachStatus?: string;
    failureReason?: string;
};

type SubTaskListResponse = {
    data?: SubTaskDTO[];
    page?: number;
    total?: number;
    pageSize?: number;
};

const { Text } = Typography;

const SubTaskList: React.FC<SubTaskListProps> = ({ filters }) => {
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 3,
    });

    // 获取子任务列表
    const {
        data: subtaskData,
        loading,
        run: fetchSubtasks,
    } = useRequest(
        async () => {
            try {
                const res = await apiCaller.post(
                    '/xianfu/api-v2/dove/data/poi/subtask',
                    { ...filters, ...pagination },
                );
                if (res.code === 0 && res.data) {
                    return res.data as SubTaskListResponse;
                } else {
                    throw new Error(res?.msg || '获取子任务列表失败');
                }
            } catch (error) {
                console.error('获取子任务列表失败:', error);
                throw error;
            }
        },
        { manual: true },
    );

    // 当visible变为true时，获取数据
    useUpdateEffect(() => {
        fetchSubtasks;
    }, [filters]);

    // 处理分页变化
    const handlePaginationChange = (page: number, pageSize?: number) => {
        setPagination({
            page,
            pageSize: pageSize || pagination.pageSize,
        });
    };

    if (loading) {
        return (
            <div className="subtask-list__loading">
                <Spin tip="加载子任务中..." />
            </div>
        );
    }

    if (!subtaskData || !subtaskData.data || subtaskData.data.length === 0) {
        return (
            <Empty
                description="暂无子任务"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
        );
    }

    return <div className="subtask-list"></div>;
};

export default SubTaskList;
