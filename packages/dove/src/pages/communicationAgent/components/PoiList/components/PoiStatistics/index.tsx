import React from 'react';
import { Card, Row, Col, Statistic, Spin, Empty } from 'antd';
import {
    ClockCircleOutlined,
    SyncOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    FileTextOutlined,
} from '@ant-design/icons';
import { useRequest, useUpdateEffect } from 'ahooks';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import './index.scss';

type PoiStatistic =
    APISpec['/xianfu/api-v2/dove/data/poi/subtask/statistics']['response'];

const PoiStatistics = ({ filters }) => {
    const {
        data,
        loading,
        run: fetchStatsData,
    } = useRequest(
        async filters => {
            try {
                const response = await apiCaller.post(
                    '/xianfu/api-v2/dove/data/poi/subtask/statistics',
                    filters,
                );

                if (response.code === 0 && response.data) {
                    return response.data;
                } else {
                    throw new Error(response.message || '获取统计数据失败');
                }
            } catch (error) {
                console.error('获取子任务统计失败:', error);
                throw error;
            }
        },
        {
            manual: true,
            onSuccess: data => {
                console.log('统计数据获取成功:', data);
            },
            onError: error => {
                console.error('统计数据获取失败:', error);
            },
        },
    );

    // 监听筛选条件变化，重新获取统计数据
    useUpdateEffect(() => {
        fetchStatsData(filters);
    }, [filters]);

    if (!data) {
        return (
            <Card className="poi-statistics">
                <Empty
                    description="暂无统计数据"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            </Card>
        );
    }

    // 统计卡片配置
    const statisticsCards = [
        {
            title: '总任务数',
            value: data.totalCount,
            icon: <FileTextOutlined />,
            color: '#1890ff',
            suffix: '个',
        },
        {
            title: '待处理',
            value: data.pendingCount,
            icon: <ClockCircleOutlined />,
            color: '#faad14',
            suffix: '个',
        },
        {
            title: '进行中',
            value: data.inProgressCount,
            icon: <SyncOutlined spin />,
            color: '#52c41a',
            suffix: '个',
        },
        {
            title: '已完成',
            value: data.completedCount,
            icon: <CheckCircleOutlined />,
            color: '#52c41a',
            suffix: '个',
        },
        {
            title: '失败',
            value: data.failedCount,
            icon: <CloseCircleOutlined />,
            color: '#ff4d4f',
            suffix: '个',
        },
    ];

    // 计算完成率
    const completionRate =
        data.totalCount > 0
            ? ((data.completedCount / data.totalCount) * 100).toFixed(1)
            : '0.0';

    return (
        <div className="poi-statistics">
            {/* 主要统计指标 */}
            <Row gutter={16} className="poi-statistics__main">
                {statisticsCards.map((card, index) => (
                    <Col span={4} key={index}>
                        <Card className="poi-statistics__card">
                            <Statistic
                                title={card.title}
                                value={card.value}
                                prefix={
                                    <span style={{ color: card.color }}>
                                        {card.icon}
                                    </span>
                                }
                                suffix={card.suffix}
                                valueStyle={{
                                    color: card.color,
                                    fontSize: '24px',
                                    fontWeight: 'bold',
                                }}
                            />
                        </Card>
                    </Col>
                ))}
                {/* 完成率 */}
                <Col span={4}>
                    <Card className="poi-statistics__card">
                        <Statistic
                            title="完成率"
                            value={completionRate}
                            suffix="%"
                            prefix={
                                <CheckCircleOutlined
                                    style={{ color: '#52c41a' }}
                                />
                            }
                            valueStyle={{
                                color: '#52c41a',
                                fontSize: '24px',
                                fontWeight: 'bold',
                            }}
                        />
                    </Card>
                </Col>
            </Row>
            {/* 任务类型统计 */}
            {data.taskTypeStats &&
                Object.keys(data.taskTypeStats).length > 0 && (
                    <Card
                        title="任务类型统计"
                        className="poi-statistics__task-types"
                        size="small"
                    >
                        <Row gutter={16}>
                            {Object.entries(data.taskTypeStats).map(
                                ([taskType, stats]) => (
                                    <Col span={6} key={taskType}>
                                        <Card className="poi-statistics__task-type-card">
                                            <div className="task-type-header">
                                                <span className="task-type-name">
                                                    {getTaskTypeName(taskType)}
                                                </span>
                                                <span className="task-type-total">
                                                    共 {stats.total} 个
                                                </span>
                                            </div>
                                            <div className="task-type-details">
                                                <div className="task-type-item">
                                                    <span className="label">
                                                        待处理:
                                                    </span>
                                                    <span className="value pending">
                                                        {stats.pending}
                                                    </span>
                                                </div>
                                                <div className="task-type-item">
                                                    <span className="label">
                                                        进行中:
                                                    </span>
                                                    <span className="value in-progress">
                                                        {stats.inProgress}
                                                    </span>
                                                </div>
                                                <div className="task-type-item">
                                                    <span className="label">
                                                        已完成:
                                                    </span>
                                                    <span className="value completed">
                                                        {stats.completed}
                                                    </span>
                                                </div>
                                                <div className="task-type-item">
                                                    <span className="label">
                                                        失败:
                                                    </span>
                                                    <span className="value failed">
                                                        {stats.failed}
                                                    </span>
                                                </div>
                                            </div>
                                        </Card>
                                    </Col>
                                ),
                            )}
                        </Row>
                    </Card>
                )}
        </div>
    );
};

// 获取任务类型中文名称
const getTaskTypeName = (taskType: string): string => {
    const taskTypeMap: Record<string, string> = {
        DATA_COLLECTION: '数据采集',
        QUALITY_CHECK: '质量检查',
        CONTENT_REVIEW: '内容审核',
        PHONE_VERIFICATION: '电话核实',
        ADDRESS_VERIFICATION: '地址核实',
    };
    return taskTypeMap[taskType] || taskType;
};

export default PoiStatistics;
