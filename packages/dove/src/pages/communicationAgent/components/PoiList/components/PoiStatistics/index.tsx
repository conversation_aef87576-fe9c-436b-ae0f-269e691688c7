import React from 'react';
import { Card, Row, Col, Statistic, Spin, Empty } from 'antd';
import {
    ClockCircleOutlined,
    SyncOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    FileTextOutlined,
} from '@ant-design/icons';
import { useRequest, useUpdateEffect } from 'ahooks';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import './index.scss';

type PoiStatistic =
    APISpec['/xianfu/api-v2/dove/data/poi/subtask/statistics']['response'];

const PoiStatistics = ({ filters }) => {
    const {
        data,
        loading,
        run: fetchStatsData,
    } = useRequest(
        async filters => {
            try {
                const response = await apiCaller.post(
                    '/xianfu/api-v2/dove/data/poi/subtask/statistics',
                    filters,
                );

                if (response.code === 0 && response.data) {
                    return response.data;
                } else {
                    throw new Error(response.message || '获取统计数据失败');
                }
            } catch (error) {
                console.error('获取子任务统计失败:', error);
                throw error;
            }
        },
        {
            manual: true,
            onSuccess: data => {
                console.log('统计数据获取成功:', data);
            },
            onError: error => {
                console.error('统计数据获取失败:', error);
            },
        },
    );

    // 监听筛选条件变化，重新获取统计数据
    useUpdateEffect(() => {
        fetchStatsData(filters);
    }, [filters]);

    const statisticItems = [
        {
            title: '任务总数',
            value: data?.totalTaskCount || 0,
            icon: <FileTextOutlined />,
        },
        {
            title: 'AI任务',
            value: data?.aiTask || 0,
            icon: <SyncOutlined />,
        },
        {
            title: '电销任务',
            value: data?.telemarketingTask || 0,
            icon: <CheckCircleOutlined />,
        },
        {
            title: 'BD任务',
            value: data?.bdTask || 0,
            icon: <CloseCircleOutlined />,
        },
    ];

    if (loading) {
        return (
            <Card className="poi-statistics">
                <Spin />
            </Card>
        );
    }

    if (!data) {
        return (
            <Card className="poi-statistics">
                <Empty
                    description="暂无统计数据"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            </Card>
        );
    }

    return (
        <Card className="poi-statistics">
            <Row gutter={16}>
                {statisticItems.map(item => (
                    <Col span={6} key={item.title}>
                        <Statistic
                            title={item.title}
                            value={item.value}
                            prefix={item.icon}
                        />
                    </Col>
                ))}
            </Row>
        </Card>
    );
};

// 获取任务类型中文名称
const getTaskTypeName = (taskType: string): string => {
    const taskTypeMap: Record<string, string> = {
        DATA_COLLECTION: '数据采集',
        QUALITY_CHECK: '质量检查',
        CONTENT_REVIEW: '内容审核',
        PHONE_VERIFICATION: '电话核实',
        ADDRESS_VERIFICATION: '地址核实',
    };
    return taskTypeMap[taskType] || taskType;
};

export default PoiStatistics;
