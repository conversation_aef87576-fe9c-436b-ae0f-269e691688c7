import React from 'react';
import { Button, Progress, Spin, Empty } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { useRequest, useDeepCompareEffect } from 'ahooks';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import './index.scss';

interface PoiStatisticsProps {
    filters?: APISpec['/xianfu/api-v2/dove/data/poi/subtask/statistics']['request'];
}

type PoiStatistic =
    APISpec['/xianfu/api-v2/dove/data/poi/subtask/statistics']['response'];

const PoiStatistics: React.FC<PoiStatisticsProps> = ({ filters }) => {
    const {
        data,
        loading,
        run: fetchStatsData,
    } = useRequest(
        async filters => {
            try {
                const res = await apiCaller.post(
                    '/xianfu/api-v2/dove/data/poi/subtask/statistics',
                    filters,
                );
                console.log('res', res);
                if (res?.code === 0 && res?.data) {
                    return {
                        ...res.data,
                        aiTaskTotal:
                            (res.data.aiNodeRunningCount || 0) +
                            (res.data.aiNodeCompletedCount || 0),
                        telemarketingTaskTotal:
                            (res.data.teleSalesNodeRunningCount || 0) +
                            (res.data.teleSalesNodeCompletedCount || 0),
                    };
                }
                return null;
            } catch (error) {
                console.error('获取子任务统计失败:', error);
            }
        },
        {
            manual: true,
            debounceWait: 500,
        },
    );

    // 监听筛选条件变化，重新获取统计数据
    useDeepCompareEffect(() => {
        if (!filters?.subtaskFilter) return;
        fetchStatsData(filters);
    }, [filters]);

    if (loading) {
        return (
            <div className="poi-statistics__loading">
                <Spin size="large" tip="加载统计数据中..." />
            </div>
        );
    }

    if (!data) {
        return (
            <Empty
                description="暂无统计数据"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
        );
    }

    // 计算进度百分比
    const calculateProgress = (current: number, total: number) => {
        if (!total || total === 0) return 0;
        return Math.round((current / total) * 100);
    };

    // 处理新建任务点击
    const handleCreateTask = () => {
        // TODO: 实现新建任务逻辑
        console.log('新建任务');
    };

    return (
        <div className="poi-statistics__container">
            {/* 新建任务按钮 */}
            <div className="poi-statistics__create">
                <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={handleCreateTask}
                    className="create-task-btn"
                >
                    新建任务
                </Button>
            </div>

            {/* 任务总数 */}
            <div className="poi-statistics__total">
                <div className="total-label">任务总数</div>
                <div className="total-value">{data.totalCount || 0}</div>
            </div>

            {/* AI任务统计 */}
            <div className="poi-statistics__item">
                <div className="item-header">
                    <span className="item-title">AI任务</span>
                    <span className="item-value">{data.aiTaskTotal}</span>
                </div>
                <div className="item-progress">
                    <Progress
                        percent={calculateProgress(
                            data.aiTaskTotal,
                            data.totalCount || 0,
                        )}
                        strokeColor="#685CFF"
                        trailColor="#E1DEFF"
                        showInfo={false}
                        size="small"
                    />
                    <div className="progress-info">
                        <span className="progress-text">进行中</span>
                        <span className="progress-text">
                            已完成 {data.aiNodeCompletedCount || 0}
                        </span>
                    </div>
                </div>
            </div>

            {/* 电销任务统计 */}
            <div className="poi-statistics__item">
                <div className="item-header">
                    <span className="item-title">电销任务</span>
                    <span className="item-value">
                        {data.telemarketingTaskTotal}
                    </span>
                </div>
                <div className="item-progress">
                    <Progress
                        percent={calculateProgress(
                            data.telemarketingTaskTotal,
                            data.totalCount || 0,
                        )}
                        strokeColor="#685CFF"
                        trailColor="#E1DEFF"
                        showInfo={false}
                        size="small"
                    />
                    <div className="progress-info">
                        <span className="progress-text">进行中</span>
                        <span className="progress-text">
                            已完成 {data.teleSalesNodeCompletedCount || 0}
                        </span>
                    </div>
                </div>
            </div>

            {/* BD任务统计 */}
            <div className="poi-statistics__total">
                <div className="total-label">BD任务</div>
                <div className="total-value">{data.bdNodeCount || 0}</div>
            </div>
        </div>
    );
};

export default PoiStatistics;
