import React, { useState } from 'react';
import {
    Card,
    Avatar,
    Tag,
    Button,
    Space,
    Typography,
    Rate,
    Divider,
} from 'antd';
import {
    ShopOutlined,
    PhoneOutlined,
    EnvironmentOutlined,
    ClockCircleOutlined,
    DownOutlined,
    UpOutlined,
} from '@ant-design/icons';

import SubTaskList from '../SubTaskList';
import type { PoiCardProps } from '../../types';
import './index.scss';

const { Text, Paragraph } = Typography;

const PoiCard: React.FC<PoiCardProps> = ({ poi }) => {
    const [subtaskVisible, setSubtaskVisible] = useState(false);

    // 获取状态标签颜色
    const getStatusColor = (status?: string) => {
        const statusColorMap: Record<string, string> = {
            ACTIVE: 'green',
            INACTIVE: 'red',
            PENDING: 'orange',
            SUSPENDED: 'gray',
        };
        return statusColorMap[status || ''] || 'default';
    };

    // 获取状态文本
    const getStatusText = (status?: string) => {
        const statusTextMap: Record<string, string> = {
            ACTIVE: '营业中',
            INACTIVE: '已关闭',
            PENDING: '待审核',
            SUSPENDED: '已暂停',
        };
        return statusTextMap[status || ''] || status || '未知';
    };

    // 切换子任务列表显示
    const toggleSubtasks = () => {
        setSubtaskVisible(!subtaskVisible);
    };

    return (
        <Card className="poi-card">
            <div className="poi-card__header">
                <div className="poi-card__avatar">
                    <Avatar
                        size={64}
                        src={poi.logoUrl}
                        icon={<ShopOutlined />}
                    />
                </div>

                <div className="poi-card__info">
                    <div className="poi-card__title">
                        <Text strong className="poi-name">
                            {poi.name}
                        </Text>
                        <Tag color={getStatusColor(poi.operatingStatus)}>
                            {getStatusText(poi.operatingStatus)}
                        </Tag>
                    </div>

                    <div className="poi-card__meta">
                        <Space direction="vertical" size="small">
                            <div className="poi-card__meta-item">
                                <Text type="secondary" className="poi-id">
                                    ID: {poi.id}
                                </Text>
                                {poi.category && (
                                    <Tag className="poi-category">
                                        {poi.category}
                                    </Tag>
                                )}
                            </div>
                        </Space>
                    </div>
                </div>

                <div className="poi-card__actions">
                    <Button
                        type="primary"
                        size="small"
                        icon={
                            subtaskVisible ? <UpOutlined /> : <DownOutlined />
                        }
                        onClick={toggleSubtasks}
                    >
                        {subtaskVisible ? '收起任务' : '查看任务'}
                    </Button>
                </div>
            </div>

            <div className="poi-card__details">
                <Space
                    direction="vertical"
                    size="small"
                    style={{ width: '100%' }}
                >
                    {poi.address && (
                        <div className="poi-card__detail-item">
                            <EnvironmentOutlined className="poi-card__detail-icon" />
                            <Paragraph
                                ellipsis={{ rows: 1, tooltip: poi.address }}
                                className="poi-card__detail-text"
                            >
                                {poi.address}
                            </Paragraph>
                        </div>
                    )}
                </Space>
            </div>

            {/* 子任务列表 */}
            {subtaskVisible && (
                <>
                    <Divider />
                    <div className="poi-card__subtasks">
                        <SubTaskList
                            poiId={poi.id || ''}
                            visible={subtaskVisible}
                        />
                    </div>
                </>
            )}
        </Card>
    );
};

export default PoiCard;
