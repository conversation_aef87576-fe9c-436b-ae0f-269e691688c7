import React, { useState } from 'react';
import { Card, Avatar, Tag, Button, Space, Typography, Row, Col } from 'antd';
import {
    ShopOutlined,
    EnvironmentOutlined,
    DownOutlined,
    UpOutlined,
} from '@ant-design/icons';

import SubTaskList from '../SubTaskList';
import type { PoiCardProps } from '../../types';
import './index.scss';

const { Text, Paragraph } = Typography;

const PoiCard: React.FC<PoiCardProps> = ({ poi }) => {
    return <div></div>;
};

export default PoiCard;
