import React, { useMemo } from 'react';
import { Card, Avatar, Tag, Space, Typography, Row, Col } from 'antd';
import { ShopOutlined, RightOutlined } from '@ant-design/icons';
import SubTaskList from '../SubTaskList';
import './index.scss';

const { Text } = Typography;

const PoiCard = ({ poi }) => {
    const poiBaseInfo = useMemo(() => {
        const keys = [
            'category',
            'type',
            'claimStatus',
            'claimedBy',
            'operatingStatus',
            'listingStatus',
        ];
        return keys.map(key => ({
            value: poi[key],
            label: poi[key],
        }));
    }, [poi]);

    const poiDetail = useMemo(() => {
        return [
            {
                label: `城市：${poi.city}`,
                value: poi.city,
            },
            {
                label: `蜂窝：${poi.aor}`,
                value: poi.aor,
            },
            {
                label: `地址：${poi.address}`,
                value: poi.address,
                isAddress: true,
            },
            {
                label: poi.assignee
                    ? `门店责任人: ${poi.assignee}`
                    : `门店认领人: ${poi.claimedBy}`,
                value: poi.assignee || poi.claimedBy,
            },
        ];
    }, [poi]);

    return (
        <Card className="poi-card" hoverable>
            <Row gutter={24} className="poi-card__content">
                {/* 左侧：商家基本信息 */}
                <Col span={7} className="poi-card__left">
                    <div className="poi-info">
                        {/* 商家头部信息 */}
                        <div className="poi-info__header">
                            <Avatar
                                size={48}
                                shape="square"
                                src={poi.logoUrl}
                                icon={<ShopOutlined />}
                                className="poi-info__avatar"
                            />
                            <div className="poi-info__title">
                                <div className="poi-info__name">
                                    <Text
                                        strong
                                        ellipsis={{ tooltip: poi.name }}
                                    >
                                        {poi.name || '未知商家'} ID: {poi.id}
                                    </Text>
                                    <RightOutlined className="poi-info__arrow" />
                                </div>
                                {/* 标签区域 */}
                                <div className="poi-info__tags">
                                    <Space wrap>
                                        {poiBaseInfo
                                            .filter(tag => tag.value)
                                            .map((tag, index) => (
                                                <Tag key={index}>
                                                    {tag.label}
                                                </Tag>
                                            ))}
                                    </Space>
                                </div>
                            </div>
                        </div>
                        {/* 详细信息 */}
                        <div className="poi-info__details">
                            {poiDetail
                                .filter(item => item.value)
                                .map((item, index) => (
                                    <Text
                                        type="secondary"
                                        ellipsis={{
                                            tooltip: item.label,
                                        }}
                                        key={index}
                                    >
                                        {item.label}
                                    </Text>
                                ))}
                        </div>
                    </div>
                </Col>

                {/* 右侧：子任务列表 */}
                <Col span={16} className="poi-card__right">
                    <div className="subtask-container">
                        <div className="subtask-container__content">
                            {poi.id && <SubTaskList poi={poi} />}
                        </div>
                    </div>
                </Col>
            </Row>
        </Card>
    );
};

export default PoiCard;
