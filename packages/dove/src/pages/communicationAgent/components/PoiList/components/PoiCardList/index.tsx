import React from 'react';
import { List, Spin, Empty } from 'antd';

import PoiCard from '../PoiCard';
import './index.scss';

interface PoiCardListProps {
    data?: any[];
    loading?: boolean;
}

const PoiCardList: React.FC<PoiCardListProps> = ({ data, loading }) => {
    // 空数据状态
    if (!data || data.length === 0) {
        return (
            <div className="poi-card-list">
                <Empty
                    description="暂无商家数据"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            </div>
        );
    }

    // 正常数据渲染
    return (
        <div className="poi-card-list">
            <Spin spinning={loading}>
                <List
                    className="poi-card-list__list"
                    dataSource={data}
                    renderItem={poi => (
                        <List.Item key={poi.id} className="poi-card-list__item">
                            <PoiCard poi={poi} />
                        </List.Item>
                    )}
                    split={false}
                />
            </Spin>
        </div>
    );
};

export default PoiCardList;
