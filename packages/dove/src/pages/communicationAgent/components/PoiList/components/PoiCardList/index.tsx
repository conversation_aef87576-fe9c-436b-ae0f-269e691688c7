import React from 'react';
import { List, Spin, Empty, <PERSON><PERSON>, But<PERSON> } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import PoiCard from '../PoiCard';
import type { PoiCardListProps } from '../../types';
import './index.scss';

const PoiCardList: React.FC<PoiCardListProps> = ({ data, loading }) => {
    // 加载状态
    if (loading) {
        return (
            <div className="poi-card-list">
                <div className="poi-card-list__loading">
                    <Spin size="large" tip="加载中..." />
                </div>
            </div>
        );
    }

    // 空数据状态
    if (!data || data.length === 0) {
        return (
            <div className="poi-card-list">
                <Empty
                    description="暂无商家数据"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            </div>
        );
    }

    return (
        <div className="poi-card-list">
            <List
                dataSource={data}
                renderItem={poi => (
                    <List.Item key={poi.id || Math.random().toString()}>
                        <PoiCard poi={poi} />
                    </List.Item>
                )}
                className="poi-card-list__list"
            />
        </div>
    );
};

export default PoiCardList;
