import { apiCaller } from '@mfe/cc-api-caller-pc';
import type {
    PoiPageQueryRequest,
    PageResponse,
    PoiDto,
    SubtaskPageQueryRequest,
    SubtaskDto,
    SubtaskStatisticsDTO,
    ApiResponse,
} from '../types';

// API基础路径
const API_BASE = '/xianfu/api-v2/dove/data';

/**
 * 获取子任务统计数据
 */
export const getSubtaskStatistics = async (
    params: Partial<PoiPageQueryRequest>,
): Promise<SubtaskStatisticsDTO> => {
    try {
        const response = await apiCaller.post(
            '/xianfu/api-v2/dove/data/poi/subtask/statistics',
            params,
        );

        if (response.code === 0 && response.data) {
            return response.data;
        } else {
            throw new Error(response.message || '获取统计数据失败');
        }
    } catch (error) {
        console.error('获取子任务统计失败:', error);
        throw error;
    }
};

/**
 * 获取商家子任务列表
 */
export const getSubtaskList = async (
    params: SubtaskPageQueryRequest,
): Promise<PageResponse<SubtaskDto>> => {
    try {
        const response = await apiCaller.post(
            '/xianfu/api-v2/dove/data/poi/subtask',
            params,
        );

        if (response.code === 0 && response.data) {
            return response.data;
        } else {
            throw new Error(response.message || '获取子任务列表失败');
        }
    } catch (error) {
        console.error('获取子任务列表失败:', error);
        throw error;
    }
};

/**
 * 获取任务类型选项
 */
export const getTaskTypeOptions = async (): Promise<
    Array<{ label: string; value: string }>
> => {
    try {
        const response = await apiCaller.get<
            ApiResponse<Array<{ label: string; value: string }>>
        >(`${API_BASE}/poi/subtask/task-types`);

        if (response.success && response.data) {
            return response.data;
        } else {
            // 返回默认选项
            return [
                { label: '数据采集', value: 'DATA_COLLECTION' },
                { label: '质量检查', value: 'QUALITY_CHECK' },
                { label: '内容审核', value: 'CONTENT_REVIEW' },
                { label: '电话核实', value: 'PHONE_VERIFICATION' },
                { label: '地址核实', value: 'ADDRESS_VERIFICATION' },
            ];
        }
    } catch (error) {
        console.error('获取任务类型选项失败:', error);
        // 返回默认选项
        return [
            { label: '数据采集', value: 'DATA_COLLECTION' },
            { label: '质量检查', value: 'QUALITY_CHECK' },
            { label: '内容审核', value: 'CONTENT_REVIEW' },
            { label: '电话核实', value: 'PHONE_VERIFICATION' },
            { label: '地址核实', value: 'ADDRESS_VERIFICATION' },
        ];
    }
};
