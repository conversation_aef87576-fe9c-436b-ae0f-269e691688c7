import React, { useState } from 'react';
import { useRequest, useUpdateEffect } from 'ahooks';
import { Pagination } from 'antd';
import PoiFilter from './components/PoiFilter';
import PoiStatistics from './components/PoiStatistics';
import PoiCardList from './components/PoiCardList';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import PoiListContext, { PoiQueryReq } from './context';
import './index.scss';

type PoiList = APISpec['/xianfu/api-v2/dove/data/poi/search']['response'];

const PoiList: React.FC = () => {
    const [filters, setFilters] = useState<PoiQueryReq>({
        page: 1,
        pageSize: 10,
    });
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 10,
    });

    // 商家列表数据请求
    const {
        data: poiData,
        loading: poiLoading,
        run: fetchPoiData,
    } = useRequest(
        async (extra = {}) => {
            try {
                const res = await apiCaller.post(
                    '/xianfu/api-v2/dove/data/poi/search',
                    {
                        ...filters,
                        ...pagination,
                        ...extra,
                    },
                );
                if (res?.code === 0 && res.data) {
                    return res.data;
                }
                // 如果接口返回错误，抛出异常
                throw new Error(res?.msg || '获取商家列表失败');
            } catch (error) {
                console.error('搜索商家列表失败:', error);
                throw error;
            }
        },
        { manual: true },
    );

    // 处理筛选条件变化
    const handleFilterChange = (newFilters: Partial<PoiQueryReq>) => {
        setFilters(prev => ({ ...prev, page: 1, ...newFilters }));
        // 筛选条件变化时回到第一页
        setPagination(p => ({ ...p, page: 1 }));
    };

    useUpdateEffect(() => {
        fetchPoiData();
    }, [filters]);

    // 处理分页变化
    const handlePaginationChange = (page: number, pageSize?: number) => {
        setPagination({
            page,
            pageSize: pageSize || pagination.pageSize,
        });
        fetchPoiData({
            page,
            pageSize: pageSize || pagination.pageSize,
        });
    };

    return (
        <div className="poi-list">
            {/* 筛选区 */}
            <div className="poi-list__filter">
                <PoiFilter onChange={handleFilterChange} />
            </div>
            <PoiListContext.Provider value={{ filters }}>
                {/* 统计区 */}
                <div className="poi-list__statistics poi-statistics">
                    <PoiStatistics />
                </div>
                {/* 列表区 */}
                <PoiCardList data={poiData?.data} loading={poiLoading} />
            </PoiListContext.Provider>

            {/* 分页区 */}
            {poiData?.total && poiData.total > 0 && (
                <div className="poi-list__pagination">
                    <Pagination
                        current={pagination.page}
                        pageSize={pagination.pageSize}
                        total={poiData.total}
                        showSizeChanger
                        showQuickJumper
                        showTotal={(total, range) =>
                            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
                        }
                        onChange={handlePaginationChange}
                        onShowSizeChange={handlePaginationChange}
                    />
                </div>
            )}
        </div>
    );
};

export default PoiList;
