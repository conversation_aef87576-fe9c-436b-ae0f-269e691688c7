import React, { useState } from 'react';
import { useRequest } from 'ahooks';
import { Pagination } from 'antd';
import PoiFilter from './components/PoiFilter';
import PoiStatistics from './components/PoiStatistics';
import PoiCardList from './components/PoiCardList';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import type { PoiPageQueryRequest } from './types';
import './index.scss';

type PoiList = APISpec['/xianfu/api-v2/dove/data/poi/search']['response'];

const PoiList: React.FC = () => {
    const [filters, setFilters] = useState<PoiPageQueryRequest>({
        page: 1,
        pageSize: 10,
    });
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 10,
    });

    // 商家列表数据请求
    const {
        data: poiData,
        loading: poiLoading,
        run: fetchPoiData,
    } = useRequest(
        async (extra = {}) => {
            try {
                const res = await apiCaller.post(
                    '/xianfu/api-v2/dove/data/poi/search',
                    {
                        ...filters,
                        ...pagination,
                        ...extra,
                    },
                );
                if (res?.code === 0 && res.data) {
                    // 转换API响应数据格式以匹配PoiDto接口
                    const transformedData = {
                        ...res.data,
                        data:
                            res.data.data?.map((item: any) => ({
                                poiId: item.id || '',
                                poiName: item.name || '',
                                address: item.address,
                                category: item.category,
                                status: item.operatingStatus,
                                imageUrl: item.logoUrl,
                                // 保留其他字段
                                ...item,
                            })) || [],
                        total: res.data.total || 0,
                        page: res.data.page || pagination.page,
                        pageSize: res.data.pageSize || pagination.pageSize,
                    };
                    return transformedData;
                }
                return {
                    data: [],
                    total: 0,
                    page: pagination.page,
                    pageSize: pagination.pageSize,
                };
            } catch (error) {
                console.error('搜索商家列表失败:', error);
                throw error;
            }
        },
        { manual: true },
    );

    // 处理筛选条件变化
    const handleFilterChange = (newFilters: Partial<PoiPageQueryRequest>) => {
        setFilters(prev => ({ ...prev, page: 1, ...newFilters }));
        // 筛选条件变化时回到第一页
        setPagination(p => ({ ...p, page: 1 }));
    };

    // 处理分页变化
    const handlePaginationChange = (page: number, pageSize?: number) => {
        setPagination({
            page,
            pageSize: pageSize || pagination.pageSize,
        });
        fetchPoiData({
            page,
            pageSize: pageSize || pagination.pageSize,
        });
    };

    return (
        <div className="poi-list">
            {/* 筛选区 */}
            <div className="poi-list__filter">
                <PoiFilter
                    initialValues={filters}
                    onChange={handleFilterChange}
                />
            </div>

            {/* 统计区 */}
            <div className="poi-list__statistics">
                <PoiStatistics filters={filters} />
            </div>

            {/* 列表区 */}
            <div className="poi-list__content">
                <PoiCardList data={poiData?.data} loading={poiLoading} />
            </div>

            {/* 分页区 */}
            {poiData && (poiData.total || 0) > 0 && (
                <div className="poi-list__pagination">
                    <Pagination
                        current={pagination.page}
                        pageSize={pagination.pageSize}
                        total={poiData.total || 0}
                        showSizeChanger
                        showQuickJumper
                        showTotal={(total, range) =>
                            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
                        }
                        onChange={handlePaginationChange}
                        onShowSizeChange={handlePaginationChange}
                    />
                </div>
            )}
        </div>
    );
};

export default PoiList;
