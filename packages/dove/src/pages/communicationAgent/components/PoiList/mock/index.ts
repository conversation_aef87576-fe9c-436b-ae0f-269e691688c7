// Mock数据 - 商家列表页面
import type { PoiDto } from '../types';

// 商家列表Mock数据
export const mockPoiList: PoiDto[] = [
    {
        id: '1294797970',
        name: '汕头潮蟹牛肉美食店',
        aor: '中城',
        city: '四川成都',
        type: '1', // 已合作门店
        address: '双流区信和街道金河路199号',
        logoUrl: 'https://p0.meituan.net/merchantpic/default_logo.png',
        assignee: 'zhangsan',
        category: '川菜',
        operatingStatus: 'ACTIVE', // 营业中
        listingStatus: 'LISTED', // 已上单
    },
    {
        id: '1294797971',
        name: '四川饭庄',
        aor: '东城',
        city: '四川成都',
        type: '2', // 公海门店
        address: '青羊区青羊大道133号金牛万达广场',
        logoUrl: 'https://p0.meituan.net/merchantpic/sichuan_logo.png',
        claimedBy: 'zhangsan',
        category: '川菜',
        claimStatus: 'CLAIMED', // 已认领
    },
    {
        id: '1294797972',
        name: '重庆火锅城',
        aor: '西城',
        city: '重庆',
        type: '1', // 已合作门店
        address: '渝中区解放碑步行街88号',
        logoUrl: 'https://p0.meituan.net/merchantpic/hotpot_logo.png',
        assignee: 'lisi',
        category: '火锅',
        operatingStatus: 'INACTIVE', // 已关闭
        listingStatus: 'UNLISTED', // 未上单
    },
    {
        id: '1294797973',
        name: '北京烤鸭店',
        aor: '北城',
        city: '北京',
        type: '2', // 公海门店
        address: '朝阳区三里屯太古里南区',
        logoUrl: 'https://p0.meituan.net/merchantpic/duck_logo.png',
        claimedBy: 'wangwu',
        category: '京菜',
        claimStatus: 'PENDING', // 待认领
    },
    {
        id: '1294797974',
        name: '广东茶餐厅',
        aor: '南城',
        city: '广州',
        type: '1', // 已合作门店
        address: '天河区珠江新城花城大道123号',
        logoUrl: 'https://p0.meituan.net/merchantpic/cantonese_logo.png',
        assignee: 'zhaoliu',
        category: '粤菜',
        operatingStatus: 'ACTIVE', // 营业中
        listingStatus: 'LISTED', // 已上单
    },
    {
        id: '1294797975',
        name: '上海小笼包',
        aor: '东城',
        city: '上海',
        type: '2', // 公海门店
        address: '黄浦区南京东路步行街456号',
        logoUrl: 'https://p0.meituan.net/merchantpic/xiaolongbao_logo.png',
        claimedBy: 'sunqi',
        category: '沪菜',
        claimStatus: 'CLAIMED', // 已认领
    },
    {
        id: '1294797976',
        name: '西安肉夹馍',
        aor: '西城',
        city: '西安',
        type: '1', // 已合作门店
        address: '雁塔区大雁塔南广场东侧',
        logoUrl: 'https://p0.meituan.net/merchantpic/roujiamo_logo.png',
        assignee: 'zhouba',
        category: '陕菜',
        operatingStatus: 'SUSPENDED', // 已暂停
        listingStatus: 'UNLISTED', // 未上单
    },
    {
        id: '1294797977',
        name: '东北饺子馆',
        aor: '北城',
        city: '哈尔滨',
        type: '2', // 公海门店
        address: '道里区中央大街789号',
        logoUrl: 'https://p0.meituan.net/merchantpic/jiaozi_logo.png',
        claimedBy: 'wujiu',
        category: '东北菜',
        claimStatus: 'PENDING', // 待认领
    },
];

// 分页响应Mock数据
export const mockPoiSearchResponse = {
    data: mockPoiList,
    page: 1,
    pageSize: 10,
    total: 459,
};
// 子任务Mock数据
export const mockSubTasks = [
    {
        id: '1294797970',
        tel: '13800138000',
        rank: 'A',
        bizId: 1001,
        status: 'COMPLETED',
        taskId: '25-05',
        agentId: 'agent001',
        bizName: '火锅招商',
        creator: 'zhangsan',
        summary:
            '2025年6月6日20点，平台商家运营管理员致电给外卖，主要沟通内容是确定商家开通意向',
        duration: 180,
        taskName: '火锅招商',
        agentName: 'AI智能助手',
        followMis: 'zhangsan',
        followUid: 'uid001',
        processId: 'process001',
        reachTime: 1714521600000,
        createTime: 1714521600000,
        contactType: 'AI_CALL',
        processName: '这里只能是结果',
        reachStatus: 'SUCCESS',
        failureReason: null,
    },
    {
        id: '1294797971',
        tel: '13800138001',
        rank: 'B',
        bizId: 1001,
        status: 'IN_PROGRESS',
        taskId: '25-05',
        agentId: 'agent002',
        bizName: '新签任务',
        creator: 'zhangsan',
        summary: '客户表示有意向，需要进一步沟通',
        duration: 120,
        taskName: '新签任务25-05',
        agentName: 'AI智能助手',
        followMis: 'zhangsan',
        followUid: 'uid001',
        processId: 'process002',
        reachTime: 1714521600000,
        createTime: 1714521600000,
        contactType: 'AI_CALL',
        processName: '这里只能是结果',
        reachStatus: 'SUCCESS',
        failureReason: null,
    },
];

// 子任务分页响应Mock
export const mockSubTaskResponse = {
    data: mockSubTasks,
    page: 1,
    pageSize: 10,
    total: 2,
};

// 状态映射
export const statusMap = {
    operatingStatus: {
        ACTIVE: '营业中',
        INACTIVE: '已关闭',
        PENDING: '待审核',
        SUSPENDED: '已暂停',
    },
    claimStatus: {
        CLAIMED: '已认领',
        PENDING: '待认领',
        UNCLAIMED: '未认领',
    },
    listingStatus: {
        LISTED: '已上单',
        UNLISTED: '未上单',
    },
    taskStatus: {
        COMPLETED: '已完成',
        IN_PROGRESS: '进行中',
        PENDING: '待执行',
        FAILED: '失败',
        CANCELLED: '已取消',
    },
};

// 状态颜色映射
export const statusColorMap = {
    operatingStatus: {
        ACTIVE: 'green',
        INACTIVE: 'red',
        PENDING: 'orange',
        SUSPENDED: 'gray',
    },
    claimStatus: {
        CLAIMED: 'blue',
        PENDING: 'orange',
        UNCLAIMED: 'default',
    },
    listingStatus: {
        LISTED: 'green',
        UNLISTED: 'red',
    },
    taskStatus: {
        COMPLETED: 'green',
        IN_PROGRESS: 'blue',
        PENDING: 'orange',
        FAILED: 'red',
        CANCELLED: 'gray',
    },
};

// Mock API 工具函数
export const mockApiUtils = {
    // 模拟分页查询
    getPagedData: <T>(data: T[], page = 1, pageSize = 10) => {
        const start = (page - 1) * pageSize;
        const end = start + pageSize;
        return {
            data: data.slice(start, end),
            page,
            pageSize,
            total: data.length,
        };
    },

    // 模拟搜索过滤
    filterPoiList: (list: PoiDto[], filters: any) => {
        return list.filter(poi => {
            if (filters.poiId && !poi.id?.includes(filters.poiId)) return false;
            if (filters.poiName && !poi.name?.includes(filters.poiName))
                return false;
            if (
                filters.assigneeList?.length &&
                !filters.assigneeList.includes(poi.assignee || poi.claimedBy)
            )
                return false;
            if (filters.cityId && poi.city !== filters.cityId) return false;
            if (filters.poiType && poi.type !== filters.poiType.toString())
                return false;
            return true;
        });
    },

    // 模拟延迟
    delay: (ms = 500) => new Promise(resolve => setTimeout(resolve, ms)),
};
