// Mock API 服务
import {
    mockPoiList,
    mockStatistics,
    mockSubTasks,
    mockApiUtils,
} from '.';
import type { PoiPageQueryRequest } from '../types';

// Mock API 响应延迟
const API_DELAY = 300;

// Mock 商家搜索API
export const mockPoiSearchApi = async (params: PoiPageQueryRequest) => {
    await mockApiUtils.delay(API_DELAY);

    // 模拟搜索过滤
    const filteredList = mockApiUtils.filterPoiList(mockPoiList, params);

    // 模拟分页
    const result = mockApiUtils.getPagedData(
        filteredList,
        params.page || 1,
        params.pageSize || 10,
    );

    return {
        code: 200,
        msg: 'success',
        ...result,
    };
};

// Mock 统计数据API
export const mockStatisticsApi = async (params: any) => {
    await mockApiUtils.delay(API_DELAY);

    return {
        code: 200,
        msg: 'success',
        data: mockStatistics,
    };
};

// Mock 子任务列表API
export const mockSubTaskApi = async (params: any) => {
    await mockApiUtils.delay(API_DELAY);

    const result = mockApiUtils.getPagedData(
        mockSubTasks,
        params.page || 1,
        params.pageSize || 10,
    );

    return {
        code: 200,
        msg: 'success',
        ...result,
    };
};

// Mock API 开关配置
export const MOCK_CONFIG = {
    enabled: process.env.NODE_ENV === 'development', // 开发环境启用Mock
    apis: {
        poiSearch: true,
        statistics: true,
        subTask: true,
    },
};

// Mock 拦截器
export const mockInterceptor = {
    // 拦截商家搜索
    '/xianfu/api-v2/dove/data/poi/search': mockPoiSearchApi,
    // 拦截统计数据
    '/xianfu/api-v2/dove/data/poi/subtask/statistics': mockStatisticsApi,
    // 拦截子任务列表
    '/xianfu/api-v2/dove/data/poi/subtask': mockSubTaskApi,
};

// 使用示例：
// import { mockPoiSearchApi } from './mock/api';
//
// const searchPoi = async (params) => {
//     if (MOCK_CONFIG.enabled && MOCK_CONFIG.apis.poiSearch) {
//         return await mockPoiSearchApi(params);
//     }
//     // 真实API调用
//     return await realApiCall(params);
// };
